// 全局翻页配置
// 统一所有数据库的翻页限制，提供最佳性能和一致的用户体验

export const GLOBAL_PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  MAX_PAGES: 100,  // 统一100页限制
} as const;

/**
 * 验证和限制翻页参数
 * @param requestedPage 请求的页码
 * @param requestedLimit 请求的每页条数
 * @returns 验证后的翻页参数
 */
export function validatePaginationParams(
  requestedPage: number,
  requestedLimit: number
) {
  const page = Math.max(1, Math.min(requestedPage, GLOBAL_PAGINATION_CONFIG.MAX_PAGES));
  const limit = requestedLimit > 0 
    ? Math.min(requestedLimit, GLOBAL_PAGINATION_CONFIG.MAX_PAGE_SIZE) 
    : GLOBAL_PAGINATION_CONFIG.DEFAULT_PAGE_SIZE;
  
  return {
    page,
    limit,
    isAtMaxPages: page >= GLOBAL_PAGINATION_CONFIG.MAX_PAGES,
    maxPages: GLOBAL_PAGINATION_CONFIG.MAX_PAGES,
    maxPageSize: GLOBAL_PAGINATION_CONFIG.MAX_PAGE_SIZE,
    defaultPageSize: GLOBAL_PAGINATION_CONFIG.DEFAULT_PAGE_SIZE,
  };
}

/**
 * 构建标准的分页响应对象
 * @param page 当前页码
 * @param limit 每页条数
 * @param totalCount 总记录数
 * @returns 标准分页响应
 */
export function buildPaginationResponse(
  page: number,
  limit: number,
  totalCount: number
) {
  const totalPages = Math.ceil(totalCount / limit);
  const paginationParams = validatePaginationParams(page, limit);

  // 计算实际可访问的最大页数
  const effectiveMaxPages = Math.min(totalPages, GLOBAL_PAGINATION_CONFIG.MAX_PAGES);

  // 修复 isAtMaxPages 逻辑：只有当前页达到有效最大页数时才为 true
  const isAtMaxPages = page >= effectiveMaxPages;

  // 修复 hasNext 逻辑：考虑总页数和最大页数限制
  const hasNext = page < effectiveMaxPages;

  return {
    page,
    limit,
    totalCount,
    totalPages,
    hasNext,
    hasPrev: page > 1,
    // 翻页限制信息
    maxPages: GLOBAL_PAGINATION_CONFIG.MAX_PAGES,
    isAtMaxPages,
    maxPageSize: GLOBAL_PAGINATION_CONFIG.MAX_PAGE_SIZE,
    defaultPageSize: GLOBAL_PAGINATION_CONFIG.DEFAULT_PAGE_SIZE,
  };
}
