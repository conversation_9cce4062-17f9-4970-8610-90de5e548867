"use client";

import React, { useState, useCallback } from 'react';
import { Search, ExternalLink } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import SearchChips from '@/components/SearchChips';
import { useSimpleSearchChips } from '@/hooks/useSearchChips';
import { useGlobalSearch } from '@/hooks/use-global-search';
import Link from 'next/link';

interface GlobalSearchWithChipsProps {
  className?: string;
}

export default function GlobalSearchWithChips({ className }: GlobalSearchWithChipsProps) {
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [currentQuery, setCurrentQuery] = useState('');
  
  // 使用全局搜索Hook
  const { results, isSearching, performSearch } = useGlobalSearch();

  // 将搜索历史转换为过滤器格式
  const historyFilters = React.useMemo(() => {
    const filters: Record<string, unknown> = {};
    searchHistory.forEach((query, index) => {
      filters[`history_${index}`] = query;
    });
    return filters;
  }, [searchHistory]);

  // 使用搜索标签Hook管理搜索历史
  const searchChips = useSimpleSearchChips(
    historyFilters,
    (key: string, value: unknown) => {
      if (value === undefined || value === null) {
        // Remove search history item
        const index = parseInt(key.replace('history_', ''));
        setSearchHistory(prev => prev.filter((_, i) => i !== index));
      }
    },
    searchHistory.map((query, index) => ({
      fieldName: `history_${index}`,
      displayName: `Search: ${query}`
    }))
  );

  // Execute search
  const handleSearch = useCallback(async () => {
    if (!currentQuery.trim()) return;

    // Add to search history
    setSearchHistory(prev => {
      const newHistory = [currentQuery, ...prev.filter(q => q !== currentQuery)];
      return newHistory.slice(0, 5); // Keep maximum 5 search history items
    });

    // Execute search
    await performSearch(currentQuery);
  }, [currentQuery, performSearch]);

  // Handle keyboard events
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    }
  };

  // Clear all search history
  const handleClearHistory = () => {
    setSearchHistory([]);
  };

  return (
    <div className={className}>
      {/* Search box */}
      <div className="flex items-center w-full max-w-2xl mx-auto mb-6">
        <div className="relative flex-1">
          <Input
            type="text"
            placeholder="Enter product name, company name, application number, registration number or title to search"
            className="h-12 text-sm md:text-base pr-16 border-r-0 rounded-r-none"
            value={currentQuery}
            onChange={(e) => setCurrentQuery(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        </div>
        <Button
          className="h-12 px-4 md:px-8 rounded-l-none bg-blue-600 hover:bg-blue-700"
          disabled={!currentQuery.trim() || isSearching}
          onClick={handleSearch}
        >
          {isSearching ? 'Searching...' : 'Search'}
        </Button>
      </div>

      {/* Search history tags */}
      {searchChips.hasActiveFilters && (
        <div className="mb-6">
          <SearchChips
            chips={searchChips.chips.map(chip => ({
              ...chip,
              displayValue: chip.displayValue.replace('Search: ', ''),
              color: 'outline' as const
            }))}
            onRemoveChip={searchChips.handleRemoveChip}
            onClearAll={handleClearHistory}
            showClearAll={true}
            maxDisplay={5}
            compact={true}
            className="bg-white border border-gray-200 rounded-lg"
          />
        </div>
      )}

      {/* Search results */}
      {results.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">
            Search Results for "{currentQuery}"
          </h3>
          
          <div className="grid gap-4 md:grid-cols-2">
            {results.map((result) => (
              <Card key={result.database} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center justify-between">
                    <span>{result.database}</span>
                    <span className="text-sm font-normal text-gray-500">
                      {result.count} results
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  {result.count > 0 ? (
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-600">
                        Found {result.count} related records
                      </div>
                      <Link
                        href={`/data/list/${result.database}?allFields=${encodeURIComponent(currentQuery)}`}
                        className="inline-flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 hover:underline"
                      >
                        View Detailed Results
                        <ExternalLink className="h-3 w-3" />
                      </Link>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500">
                      No related records found
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* No results message */}
      {results.length === 0 && currentQuery && !isSearching && (
        <div className="text-center py-8">
          <p className="text-gray-500">No results found for "{currentQuery}"</p>
          <p className="text-sm text-gray-400 mt-1">
            Please try using different keywords or check spelling
          </p>
        </div>
      )}

      {/* Usage instructions */}
      <div className="mt-8 p-4 bg-gray-50 rounded-md">
        <h4 className="font-medium text-gray-900 mb-2">Search Tags Feature Instructions</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• <strong>Search History</strong>: Automatically saves the last 5 searches, click tags to quickly re-search</li>
          <li>• <strong>Remove Tags</strong>: Click the × button on tags to remove individual search history</li>
          <li>• <strong>Clear All</strong>: Click the "Clear All" button to clear all search history</li>
          <li>• <strong>Smart Deduplication</strong>: Identical search terms won't be added to history repeatedly</li>
        </ul>
      </div>
    </div>
  );
}
